import logging

from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckResult,
)
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.api import (
    NozomiVantageV1Api,
    NozomiVantageV1ApiError,
)

logger = logging.getLogger(__name__)


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    """Health check for Nozomi Vantage API connection."""

    def get_result(self) -> IntegrationHealthCheckResult:
        """
        Perform health check by testing API connectivity and authentication.

        Returns:
            IntegrationHealthCheckResult: Result of the health check
        """
        try:
            api: NozomiVantageV1Api = self.integration.get_api()

            # Use the API's built-in health check method
            success = api.health_check()

            if success:
                logger.info("Nozomi Vantage health check passed")
                return IntegrationHealthCheckResult.PASSED
            else:
                logger.warning("Nozomi Vantage health check failed")
                return IntegrationHealthCheckResult.FAILED

        except NozomiVantageV1ApiError as e:
            logger.error(f"Nozomi Vantage API error during health check: {e}")
            return IntegrationHealthCheckResult.FAILED
        except Exception as e:
            logger.error(f"Unexpected error during Nozomi Vantage health check: {e}")
            return IntegrationHealthCheckResult.FAILED
