import logging
from typing import Dict, List

from apps.connectors.integrations.actions.update_lifecycle_status import (
    UpdateLifecycleStatus,
    UpdateLifecycleStatusArgs,
    UpdateLifecycleStatusResult,
)
from apps.connectors.integrations.schemas import Message
from apps.connectors.integrations.schemas.tap_result import ErrorDetail
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.actions import (
    NozomiVantageV1InternalActions,
)
from apps.connectors.integrations.vendors.nozomi.nozomi_vantage.v1.api import (
    NozomiVantageV1Api,
    NozomiVantageV1ApiError,
)

logger = logging.getLogger(__name__)


class NozomiVantageV1UpdateLifecycleStatus(UpdateLifecycleStatus):
    """Update lifecycle status action for Nozomi Vantage alerts."""

    name = "Update Alert Lifecycle Status"

    def execute(self, args: UpdateLifecycleStatusArgs, **kwargs) -> UpdateLifecycleStatusResult:
        """
        Execute the update lifecycle status action.

        Args:
            args: Arguments containing alert ID and status update information
            **kwargs: Additional keyword arguments

        Returns:
            UpdateLifecycleStatusResult: Result of the status update

        Raises:
            NozomiVantageV1ApiError: If the API request fails
        """
        api: NozomiVantageV1Api = self.integration.get_api()

        try:
            # Extract alert ID from vendor sync ID
            alert_id = args.vendor_sync_id

            # Determine the action based on the status
            action_type = self._determine_action_type(args.status)

            if action_type == "acknowledge":
                return self._acknowledge_alert(api, alert_id, acknowledge=True)
            elif action_type == "unacknowledge":
                return self._acknowledge_alert(api, alert_id, acknowledge=False)
            else:
                logger.warning(f"Unsupported status update: {args.status}")
                return UpdateLifecycleStatusResult(
                    error=ErrorDetail(message=f"Unsupported status update: {args.status}")
                )

        except NozomiVantageV1ApiError as e:
            logger.error(f"Failed to update lifecycle status for alert {args.vendor_sync_id}: {e}")
            return UpdateLifecycleStatusResult(
                error=ErrorDetail(message=f"Failed to update status: {str(e)}")
            )
        except Exception as e:
            logger.error(f"Unexpected error updating lifecycle status for alert {args.vendor_sync_id}: {e}")
            return UpdateLifecycleStatusResult(
                error=ErrorDetail(message=f"Unexpected error: {str(e)}")
            )

    def _determine_action_type(self, status: str) -> str:
        """
        Determine the action type based on the status string.

        Args:
            status: Status string from the update request

        Returns:
            str: Action type ("acknowledge", "unacknowledge", or "unsupported")
        """
        status_lower = status.lower()

        if "acknowledge" in status_lower and "un" not in status_lower:
            return "acknowledge"
        elif "unacknowledge" in status_lower or "un-acknowledge" in status_lower:
            return "unacknowledge"
        elif "in_progress" in status_lower or "in-progress" in status_lower:
            return "acknowledge"
        elif "new" in status_lower or "open" in status_lower:
            return "unacknowledge"
        else:
            return "unsupported"

    def _acknowledge_alert(self, api: NozomiVantageV1Api, alert_id: str, acknowledge: bool) -> UpdateLifecycleStatusResult:
        """
        Acknowledge or unacknowledge an alert.

        Args:
            api: Nozomi Vantage API client
            alert_id: Alert ID to update
            acknowledge: True to acknowledge, False to unacknowledge

        Returns:
            UpdateLifecycleStatusResult: Result of the acknowledgment operation
        """
        action = "acknowledge" if acknowledge else "unacknowledge"

        logger.info(
            f"Attempting to {action} Nozomi Vantage alert {alert_id}",
            extra={
                "alert_id": alert_id,
                "action": action,
            }
        )

        try:
            # Call the API to acknowledge/unacknowledge the alert
            response = api.acknowledge_alerts([alert_id], acknowledge=acknowledge)

            # Check if the response indicates success
            success = self._is_acknowledgment_successful(response)

            if success:
                logger.info(f"Successfully {action}d alert {alert_id}")
                message = f"Alert {alert_id} has been {action}d"
                return UpdateLifecycleStatusResult(
                    result=Message(message=message)
                )
            else:
                logger.warning(f"Acknowledgment operation may have failed for alert {alert_id}")
                message = f"Acknowledgment operation attempted for alert {alert_id}"
                return UpdateLifecycleStatusResult(
                    error=ErrorDetail(message=message)
                )

        except NozomiVantageV1ApiError as e:
            logger.error(f"Failed to {action} alert {alert_id}: {e}")
            raise

    def _is_acknowledgment_successful(self, response: Dict) -> bool:
        """
        Check if the acknowledgment was successful based on API response.

        Args:
            response: API response from acknowledge request

        Returns:
            bool: True if acknowledgment was successful
        """
        # The exact response structure may need validation during implementation
        # Common patterns for success indication:
        if "data" in response:
            return True

        if "success" in response:
            return response["success"]

        if "status" in response:
            return response["status"] in ["success", "ok", "updated"]

        # If we get a response without errors, assume success
        return True

    def get_permission_checks(self, *args, **kwargs):
        """Return permission checks required for this action."""
        # No specific permission checks defined for Nozomi Vantage yet
        # This would be implemented based on the API's permission model
        return []


class NozomiVantageV1AcknowledgeAlert(NozomiVantageV1UpdateLifecycleStatus):
    """Acknowledge alert action for Nozomi Vantage."""

    name = "Acknowledge Alert"
    action_type = NozomiVantageV1InternalActions.ACKNOWLEDGE_ALERT

    def execute(self, args: UpdateLifecycleStatusArgs, **kwargs) -> UpdateLifecycleStatusResult:
        """Execute acknowledge alert action."""
        # Override the status to ensure acknowledgment
        args.status = "acknowledge"
        return super().execute(args, **kwargs)


class NozomiVantageV1UnacknowledgeAlert(NozomiVantageV1UpdateLifecycleStatus):
    """Unacknowledge alert action for Nozomi Vantage."""

    name = "Unacknowledge Alert"
    action_type = NozomiVantageV1InternalActions.UNACKNOWLEDGE_ALERT

    def execute(self, args: UpdateLifecycleStatusArgs, **kwargs) -> UpdateLifecycleStatusResult:
        """Execute unacknowledge alert action."""
        # Override the status to ensure unacknowledgment
        args.status = "unacknowledge"
        return super().execute(args, **kwargs)
